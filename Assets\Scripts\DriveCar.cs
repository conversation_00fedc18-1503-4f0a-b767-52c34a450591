using StarterAssets;
using UnityEngine;

public class DriveCar : MonoBehaviour
{
    [Header("Car Settings")]
    public float moveSpeed = 5f;
    public float turnSpeed = 2f;
    public Transform car;
    public Transform steeringWheel;
    public float maxSteeringAngle = 30f;
    public float steeringWheelTurnSpeed = 5f;
    public Transform[] frontWheels;
    public Transform[] backWheels;
    public float wheelTurnSpeed = 5f;
    public int capacity = 4;

    public GameObject[] players;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        players = new GameObject[capacity];
    }

    // Update is called once per frame
    void Update()
    {
        if (players[0] != null)
        {
            // Check if the driver is the local player
            StarterAssetsInputs driverInputs = players[0].GetComponent<StarterAssetsInputs>();
            if (driverInputs != null)
            {
                HandleCarMovement(driverInputs);
            }
        }
    }

    private void HandleCarMovement(StarterAssetsInputs inputs)
    {
        // Get input values
        float horizontal = inputs.move.x; // A/D or Left/Right arrow keys
        float vertical = inputs.move.y;   // W/S or Up/Down arrow keys

        // Calculate movement
        Vector3 movement = Vector3.zero;
        float steering = 0f;

        if (Mathf.Abs(vertical) > 0.1f)
        {
            // Move forward/backward
            movement = transform.forward * vertical * moveSpeed * Time.deltaTime;

            // Apply steering only when moving
            if (Mathf.Abs(horizontal) > 0.1f)
            {
                steering = horizontal * turnSpeed * Time.deltaTime;
                transform.Rotate(0, steering, 0);
            }
        }

        // Move the car
        if (movement != Vector3.zero)
        {
            transform.Translate(movement, Space.World);
        }

        // Rotate wheels
        RotateWheels(vertical, horizontal);

        // Rotate steering wheel
        RotateSteeringWheel(horizontal);
    }

    private void RotateWheels(float vertical, float horizontal)
    {
        // Rotate all wheels around X-axis when moving (rolling motion)
        if (Mathf.Abs(vertical) > 0.1f)
        {
            float wheelRotation = vertical * wheelTurnSpeed * Time.deltaTime * 100f;

            // Rotate front wheels
            foreach (Transform wheel in frontWheels)
            {
                if (wheel != null)
                {
                    wheel.Rotate(wheelRotation, 0, 0);
                }
            }

            // Rotate back wheels
            foreach (Transform wheel in backWheels)
            {
                if (wheel != null)
                {
                    wheel.Rotate(wheelRotation, 0, 0);
                }
            }
        }

        // Rotate front wheels around Y-axis for steering (-30 to 30 degrees)
        if (frontWheels != null)
        {
            float targetSteeringAngle = horizontal * maxSteeringAngle;

            foreach (Transform wheel in frontWheels)
            {
                if (wheel != null)
                {
                    Vector3 currentRotation = wheel.localEulerAngles;
                    currentRotation.y = Mathf.LerpAngle(currentRotation.y, targetSteeringAngle, wheelTurnSpeed * Time.deltaTime);
                    wheel.localEulerAngles = currentRotation;
                }
            }
        }
    }

    private void RotateSteeringWheel(float horizontal)
    {
        if (steeringWheel != null)
        {
            float targetAngle = horizontal * maxSteeringAngle * 10f; // Amplify for visual effect
            Vector3 currentRotation = steeringWheel.localEulerAngles;
            currentRotation.z = Mathf.LerpAngle(currentRotation.z, targetAngle, steeringWheelTurnSpeed * Time.deltaTime);
            steeringWheel.localEulerAngles = currentRotation;
        }
    }

    public bool AddDriver(GameObject player)
    {
        if (players[0] != null) return false;
        players[0] = player;
        return true;
    }
    public bool AddPassenger(GameObject player)
    {
        if (IsFull()) return false;
        for (int i = 1; i < capacity; i++)
        {
            if (players[i] == null)
            {
                players[i] = player;
                return true;
            }
        }
        return false;
    }
    public bool RemovePlayer(GameObject player)
    {
        for (int i = 0; i < capacity; i++)
        {
            if (players[i] == player)
            {
                players[i] = null;
                return true;
            }
        }
        return false;
    }
    public bool IsFull()
    {
        for (int i = 0; i < capacity; i++)
        {
            if (players[i] == null) return false;
        }
        return true;
    }

}
