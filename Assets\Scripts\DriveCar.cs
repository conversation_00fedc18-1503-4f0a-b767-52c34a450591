using StarterAssets;
using UnityEngine;

public class DriveCar : MonoBehaviour
{
    [Header("Car Settings")]
    public float moveSpeed = 5f;
    public float turnSpeed = 2f;
    public Transform car;
    public Transform steeringWheel;
    public float maxSteeringAngle = 30f;
    public float steeringWheelTurnSpeed = 5f;
    public Transform[] frontWheels;
    public Transform[] backWheels;
    public float wheelTurnSpeed = 5f;
    public int capacity = 4;

    public GameObject[] players;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        players = new GameObject[capacity];
    }

    // Update is called once per frame
    void Update()
    {
        if (players[0] != null)
        {
            // Get input from driver
            float horizontalInput = players[0].GetComponent<StarterAssetsInputs>().move.x;
            float verticalInput = players[0].GetComponent<StarterAssetsInputs>().move.y;
    }
    public bool AddDriver(GameObject player)
    {
        if (players[0] != null) return false;
        players[0] = player;
        return true;
    }
    public bool AddPassenger(GameObject player)
    {
        if (IsFull()) return false;
        for (int i = 1; i < capacity; i++)
        {
            if (players[i] == null)
            {
                players[i] = player;
                return true;
            }
        }
        return false;
    }
    public bool RemovePlayer(GameObject player)
    {
        for (int i = 0; i < capacity; i++)
        {
            if (players[i] == player)
            {
                players[i] = null;
                return true;
            }
        }
        return false;
    }
    public bool IsFull()
    {
        for (int i = 0; i < capacity; i++)
        {
            if (players[i] == null) return false;
        }
        return true;
    }

}
