V1.9 [2023-11-10]
- fix: Always enqueue Disconnected event (imer)

V1.8 [2021-06-02]
- fix: Do not set timeouts on listener (fixes https://github.com/vis2k/Mirror/issues/2695)
- fix: #104 - Read<PERSON><PERSON><PERSON> now catches ObjectDisposedException too

V1.7 [2021-02-20]
- ReceiveTimeout: disabled by default for cases where people use Telepathy by
  itself without pings etc.

V1.6 [2021-02-10]
- configurable ReceiveTimeout to avoid TCPs high default timeout
- Server/Client receive queue limit now disconnects instead of showing a
  warning. this is necessary for load balancing to avoid situations where one
  spamming connection might fill the queue and slow down everyone else.

V1.5 [2021-02-05]
- fix: client data races & flaky tests fixed by creating a new client state
  object every time we connect. fixes data race where an old dieing thread
  might still try to modify the current state
- fix: Client.ReceiveThreadFunction catches and ignores ObjectDisposedException
  which can happen if Disconnect() closes and disposes the client, while the
  ReceiveThread just starts up and still uses the client.
- Server/Client Tick() optional enabled check for Mirror scene changing

V1.4 [2021-02-03]
- Server/Client.Tick: limit parameter added to process up to 'limit' messages.
  makes Mirror & DOTSNET transports easier to implement
- stability: Server/Client send queue limit disconnects instead of showing a
  warning. allows for load balancing. better to kick one connection and keep
  the server running than slowing everything down for everyone.

V1.3 [2021-02-02]
- perf: ReceivePipe: byte[] pool for allocation free receives (╯°□°)╯︵ ┻━┻
- fix: header buffer, payload buffer data races because they were made non
  static earlier. server threads would all access the same ones.
  => all threaded code was moved into a static ThreadFunctions class to make it
     100% obvious that there should be no shared state in the future

V1.2 [2021-02-02]
- Client/Server Tick & OnConnected/OnData/OnDisconnected events instead of
  having the outside process messages via GetNextMessage. That's easier for
  Mirror/DOTSNET and allows for allocation free data message processing later.
- MagnificientSend/RecvPipe to shield Telepathy from all the complexity
- perf: SendPipe: byte[] pool for allocation free sends (╯°□°)╯︵ ┻━┻

V1.1 [2021-02-01]
- stability: added more tests
- breaking: Server/Client.Send: ArraySegment parameter and copy internally so
  that Transports don't need to worry about it
- perf: Buffer.BlockCopy instead of Array.Copy
- perf: SendMessageBlocking puts message header directly into payload now
- perf: receiveQueues use SafeQueue instead of ConcurrentQueue to avoid
  allocations
- Common: removed static state
- perf: SafeQueue.TryDequeueAll: avoid queue.ToArray() allocations. copy into a
  list instead.
- Logger.Log/LogWarning/LogError renamed to Log.Info/Warning/Error
- MaxMessageSize is now specified in constructor to prepare for pooling
- flaky tests are ignored for now
- smaller improvements

V1.0
- first stable release