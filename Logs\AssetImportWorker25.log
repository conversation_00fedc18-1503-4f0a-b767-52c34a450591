Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker25
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker25.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19944]  Target information:

Player connection [19944]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 616118749 [EditorId] 616118749 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19944] Host joined multi-casting on [***********:54997]...
Player connection [19944] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56944
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003114 seconds.
- Loaded All Assemblies, in  0.405 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 204 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.514 seconds
Domain Reload Profiling: 918ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (179ms)
		LoadAssemblies (124ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (174ms)
				TypeCache.ScanAssembly (161ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (514ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (466ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (284ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (38ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.634 seconds
Refreshing native plugins compatible for Editor in 1.74 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.591 seconds
Domain Reload Profiling: 1223ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (153ms)
				TypeCache.ScanAssembly (138ms)
			BuildScriptInfoCaches (44ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (591ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (462ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (294ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 200 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6553 unused Assets / (7.4 MB). Loaded Objects now: 7198.
Memory consumption went from 171.4 MB to 164.0 MB.
Total: 12.910700 ms (FindLiveObjects: 0.668500 ms CreateObjectMapping: 0.789100 ms MarkObjects: 6.629800 ms  DeleteObjects: 4.820000 ms)

========================================================================
Received Import Request.
  Time since last request: 1488926.037748 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Entering Car.fbx
  artifactKey: Guid(77f447b3583cedc4c84f415d211e885d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Entering Car.fbx using Guid(77f447b3583cedc4c84f415d211e885d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6de2ba8070ea10b96bf4a7bf3a5f01d') in 0.0396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 134

========================================================================
Received Import Request.
  Time since last request: 13.916188 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Chair_01A.fbx
  artifactKey: Guid(d141e57faf675ad4282482eed2376764) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Chair_01A.fbx using Guid(d141e57faf675ad4282482eed2376764) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '285f535747b4c87825f21ec0cfa24e94') in 0.4699039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Lounge_Chair_01B.fbx
  artifactKey: Guid(837c116f868d2784daa6e5d43d8ee89b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Lounge_Chair_01B.fbx using Guid(837c116f868d2784daa6e5d43d8ee89b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7eb323f6090d91cb5899e8382d29a67') in 0.0255052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Manhole_Cover_01A.fbx
  artifactKey: Guid(fd6961dc8599d564190ddbedc49ede2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Manhole_Cover_01A.fbx using Guid(fd6961dc8599d564190ddbedc49ede2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34d62fe3341c39eb295d31a217e3747d') in 0.0258987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Metal_Chair_01A.fbx
  artifactKey: Guid(901ad51e1946998468c43df7239133ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Metal_Chair_01A.fbx using Guid(901ad51e1946998468c43df7239133ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b904aeca7ac003be7d4892ee88b2e39d') in 0.0252344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Shelf_01B.fbx
  artifactKey: Guid(8d0bf4e7795d9bb4fab66431816e0b56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Shelf_01B.fbx using Guid(8d0bf4e7795d9bb4fab66431816e0b56) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ef6072347959f0609da3c352edb028d') in 0.0244948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Minivan_01B.fbx
  artifactKey: Guid(cbb2b24abcb8ef64e852be10d24e48b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Minivan_01B.fbx using Guid(cbb2b24abcb8ef64e852be10d24e48b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4bdeca8cb232d3f82dcb97aec30b0e2') in 0.1341691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 76

========================================================================
Received Import Request.
  Time since last request: 30.524742 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Entering Car.fbx
  artifactKey: Guid(77f447b3583cedc4c84f415d211e885d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Entering Car.fbx using Guid(77f447b3583cedc4c84f415d211e885d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c4afcde69b4905086ae472a1917276a5') in 0.0048204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

========================================================================
Received Import Request.
  Time since last request: 34.245907 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim
  artifactKey: Guid(f8ddab24c34de1d48ae74896e3c362e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim using Guid(f8ddab24c34de1d48ae74896e3c362e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba84a98343ad8e8236ff7d2a9388b16f') in 0.0161467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 4.304557 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/EnterCar.anim
  artifactKey: Guid(f8ddab24c34de1d48ae74896e3c362e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/EnterCar.anim using Guid(f8ddab24c34de1d48ae74896e3c362e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba033f7f6547c02941c1a589873446f9') in 0.0173586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 28.970000 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.fbx
  artifactKey: Guid(1690acc1f5c67a9459ccc9fc1b255d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.fbx using Guid(1690acc1f5c67a9459ccc9fc1b255d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5155bebafa6a67c3ed73fe6a5c6be3b1') in 0.0062838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 8.641878 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.fbx
  artifactKey: Guid(1690acc1f5c67a9459ccc9fc1b255d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.fbx using Guid(1690acc1f5c67a9459ccc9fc1b255d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd82f35d5db70202292f43acc1aecc5a8') in 0.0097005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

========================================================================
Received Import Request.
  Time since last request: 2.413037 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim
  artifactKey: Guid(5eef4374ef1fc204ca9e28ac91c65b29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim using Guid(5eef4374ef1fc204ca9e28ac91c65b29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0be07217b0e40dd8a9a0ffaed2266d89') in 0.0115389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 3.742801 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.anim
  artifactKey: Guid(5eef4374ef1fc204ca9e28ac91c65b29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.anim using Guid(5eef4374ef1fc204ca9e28ac91c65b29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3794b986d97e5b314467005d8cdc7448') in 0.0111058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 29.247655 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Exiting Car.fbx
  artifactKey: Guid(5c0ea7b5005b2234da712c49c476c086) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Exiting Car.fbx using Guid(5c0ea7b5005b2234da712c49c476c086) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9da7049c3093c0853410a28c2710dfad') in 0.004045 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

========================================================================
Received Import Request.
  Time since last request: 6.208457 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim
  artifactKey: Guid(ae3817b5dc739ef43a6432cb9f68fd45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim using Guid(ae3817b5dc739ef43a6432cb9f68fd45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '483469cdd76a9f596bcdeace34f151e5') in 0.0176749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 4.608942 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/ExitCar.anim
  artifactKey: Guid(ae3817b5dc739ef43a6432cb9f68fd45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/ExitCar.anim using Guid(ae3817b5dc739ef43a6432cb9f68fd45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2e9e6bcd4d80fb54b23ad2f8ef68389') in 0.0176696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 97.266231 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/StarterAssetsThirdPerson.controller
  artifactKey: Guid(40db3173a05ae3242b1c182a09b0a183) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/StarterAssetsThirdPerson.controller using Guid(40db3173a05ae3242b1c182a09b0a183) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd8224a7d32921e3e9ebf589bdc97722c') in 0.0459723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.605 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.634 seconds
Domain Reload Profiling: 1240ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (635ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (443ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6552 unused Assets / (7.4 MB). Loaded Objects now: 7249.
Memory consumption went from 173.8 MB to 166.4 MB.
Total: 12.922300 ms (FindLiveObjects: 0.647400 ms CreateObjectMapping: 0.914500 ms MarkObjects: 6.400300 ms  DeleteObjects: 4.958500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 12.110833 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.fbx
  artifactKey: Guid(1690acc1f5c67a9459ccc9fc1b255d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Driving.fbx using Guid(1690acc1f5c67a9459ccc9fc1b255d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '87f990d63fd9adabf6c435e3826c48a5') in 0.0549603 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Stand--Idle.anim.fbx
  artifactKey: Guid(12e52e465ed793a4d801955e9f964a82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Stand--Idle.anim.fbx using Guid(12e52e465ed793a4d801955e9f964a82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da47944fb0fb678a2f1d7fbc0062c181') in 0.0037385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 143

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Locomotion--Walk_N.anim.fbx
  artifactKey: Guid(8269a9f8cf495034c817722ac21f309f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Locomotion--Walk_N.anim.fbx using Guid(8269a9f8cf495034c817722ac21f309f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f0f58fed08236f6ad5dda1409fbe51e0') in 0.0033421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 143

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Jump--Jump.anim.fbx
  artifactKey: Guid(98f277b0c8055e143b2fcf058d3c27dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Jump--Jump.anim.fbx using Guid(98f277b0c8055e143b2fcf058d3c27dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a8ca5b293a6bc7b552e0255b1fdd5fc2') in 0.003724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 144

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Exiting Car.fbx
  artifactKey: Guid(5c0ea7b5005b2234da712c49c476c086) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Exiting Car.fbx using Guid(5c0ea7b5005b2234da712c49c476c086) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f24b86708fb52ad0d3348a6711cf72cf') in 0.0033475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 137

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0