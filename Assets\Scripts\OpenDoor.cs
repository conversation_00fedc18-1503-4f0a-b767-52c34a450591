using UnityEngine;

public class OpenDoor : MonoBehaviour
{
    [Head<PERSON>("Door Settings")]
    public bool isOpen = false;
    public float openAngle = 90f;
    public float openSpeed = 2f;
    public Transform door;
    public OpenDoor oppositeOpenDoor;

    private Vector3 closedRotation;
    private Vector3 openRotation;

    void Start()
    {
        // Store the initial rotation as the closed position
        closedRotation = door.eulerAngles;
        // Calculate the open rotation (rotate around Y-axis)
        openRotation = closedRotation + new Vector3(0, openAngle, 0);
    }

    void Update()
    {
        if(oppositeOpenDoor.isOpen == true)
        {
            return;
        }
        // Smoothly rotate the door between open and closed positions
        Vector3 targetRotation = isOpen ? openRotation : closedRotation;
        door.eulerAngles = Vector3.Lerp(door.eulerAngles, targetRotation, openSpeed * Time.deltaTime);
    }

    // Called when another collider enters the trigger
    void OnTriggerEnter(Collider other)
    {
        if(oppositeOpenDoor.isOpen == true)
        {
            return;
        }
        // Check if the colliding object has the "Player" tag
        if (other.CompareTag("Player"))
        {
            isOpen = true;
        }
    }

    // Called when another collider exits the trigger
    void OnTriggerExit(Collider other)
    {
        if(oppositeOpenDoor.isOpen == true)
        {
            return;
        }
        // Check if the colliding object has the "Player" tag
        if (other.CompareTag("Player"))
        {
            isOpen = false;
        }
    }
}
