using System.Collections.Generic;
using UnityEngine;
using Mirror;

/*
	Documentation: https://mirror-networking.gitbook.io/docs/guides/interest-management
	API Reference: https://mirror-networking.com/docs/api/Mirror.InterestManagement.html
*/

// NOTE: Attach this component to the same object as your Network Manager.

public class #SCRIPTNAME# : InterestManagement
{
    /// <summary>
    /// Callback used by the visibility system to determine if an observer (client) can see the NetworkIdentity.
    /// If this function returns true, the network connection will be added as an observer.
    /// </summary>
    /// <param name="identity">Object to be observed (or not) by a client</param>
    /// <param name="newObserver">Network Connection of a client.</param>
    /// <returns>True if the client can see this object.</returns>
    [ServerCallback]
    public override bool OnCheckObserver(NetworkIdentity identity, NetworkConnectionToClient newObserver)
    {
        // Default behaviour of making the identity object visible to all clients.
        // Replace this code with your own logic as appropriate.
        return true;
    }

    /// <summary>
    /// Callback used by the visibility system to determine if an observer (client) can see the NetworkIdentity.
    /// Add connections to newObservers that should see the identity object.
    /// </summary>
    /// <param name="identity">Object to be observed (or not) by clients</param>
    /// <param name="newObservers">cached hashset to put the result into</param>
    [ServerCallback]
    public override void OnRebuildObservers(NetworkIdentity identity, HashSet<NetworkConnectionToClient> newObservers)
    {
        // Default behaviour of making the identity object visible to all clients.
        // Replace this code with your own logic as appropriate.
        foreach (NetworkConnectionToClient conn in NetworkServer.connections.Values)
            newObservers.Add(conn);
    }

    /// <summary>
    /// Called on the server when a new networked object is spawned.
    /// </summary>
    /// <param name="identity">NetworkIdentity of the object being spawned</param>
    [ServerCallback]
    public override void OnSpawned(NetworkIdentity identity) { }

    /// <summary>
    /// Called on the server when a networked object is destroyed.
    /// </summary>
    /// <param name="identity">NetworkIdentity of the object being destroyed</param>
    [ServerCallback]
    public override void OnDestroyed(NetworkIdentity identity) { }

    /// <summary>
    /// Callback used by the visibility system for objects on a host.
    /// Objects on a host (with a local client) cannot be disabled or destroyed when
    /// they are not visible to the local client, so this function is called to allow
    /// custom code to hide these objects.
    /// A typical implementation will disable renderer components on the object.
    /// This is only called on local clients on a host.
    /// </summary>
    /// <param name="identity">NetworkIdentity of the object being considered for visibility</param>
    /// <param name="visible">True if the identity object should be visible to the host client</param>
    [ServerCallback]
    public override void SetHostVisibility(NetworkIdentity identity, bool visible)
    {
        base.SetHostVisibility(identity, visible);
    }

    /// <summary>
    /// Called by NetworkServer in Initialize and Shutdown
    /// </summary>
    [ServerCallback]
    public override void ResetState() { }

    [ServerCallback]
    void Update()
    {
        // Here is where you'd need to evaluate if observers need to be rebuilt,
        // either for a specific object, a subset of objects, or all objects.

        // Review the code in the various Interest Management components
        // included with Mirror for inspiration:
        // - Distance Interest Management
        // - Spatial Hash Interest Management
        // - Scene Interest Management
        // - Match Interest Management
        // - Team Interest Management
    }
}
