{"Bee.Core.BuildProgramContext+BuildProgramContextOutputData": {"MaxRerunAllowed": 2147483647}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData_Out": {"Assemblies": [{"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Edgegap.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Telepathy.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/kcp2k.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp", "MovedFromExtractorFile": "C:/Users/<USER>/ONU/Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}], "LocalizeCompilerMessages": false}}