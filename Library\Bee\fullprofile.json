{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 36960, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 36960, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 36960, "tid": 69093, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 36960, "tid": 69093, "ts": 1754163022388238, "dur": 2960, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 36960, "tid": 69093, "ts": 1754163022394300, "dur": 1815, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 36960, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 36960, "tid": 1, "ts": 1754163020957665, "dur": 4749, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754163020962418, "dur": 40099, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754163021002530, "dur": 53876, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 36960, "tid": 69093, "ts": 1754163022396119, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 36960, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020953149, "dur": 258, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020953408, "dur": 1426383, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020954165, "dur": 2452, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020956628, "dur": 1480, "ph": "X", "name": "ProcessMessages 4691", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958111, "dur": 352, "ph": "X", "name": "ReadAsync 4691", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958468, "dur": 11, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958481, "dur": 21, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958504, "dur": 11, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958517, "dur": 473, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958993, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020958994, "dur": 186, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959183, "dur": 7, "ph": "X", "name": "ProcessMessages 12714", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959190, "dur": 100, "ph": "X", "name": "ReadAsync 12714", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959293, "dur": 147, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959444, "dur": 2, "ph": "X", "name": "ProcessMessages 1391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959447, "dur": 79, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959530, "dur": 3, "ph": "X", "name": "ProcessMessages 2591", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959534, "dur": 118, "ph": "X", "name": "ReadAsync 2591", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959659, "dur": 2, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959662, "dur": 46, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959710, "dur": 2, "ph": "X", "name": "ProcessMessages 2125", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959713, "dur": 53, "ph": "X", "name": "ReadAsync 2125", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959768, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959792, "dur": 18, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959812, "dur": 101, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959917, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959920, "dur": 33, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959954, "dur": 1, "ph": "X", "name": "ProcessMessages 2181", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959956, "dur": 22, "ph": "X", "name": "ReadAsync 2181", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959981, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020959985, "dur": 25, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960013, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960033, "dur": 27, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960063, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960081, "dur": 16, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960100, "dur": 17, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960119, "dur": 17, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960138, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960155, "dur": 20, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960177, "dur": 15, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960194, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960214, "dur": 15, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960232, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960248, "dur": 14, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960263, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960281, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960300, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960319, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960342, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960369, "dur": 19, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960390, "dur": 15, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960407, "dur": 14, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960423, "dur": 25, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960450, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960490, "dur": 20, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960512, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960530, "dur": 16, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960548, "dur": 17, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960568, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960584, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960601, "dur": 77, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960679, "dur": 1, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960680, "dur": 15, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960698, "dur": 14, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960715, "dur": 19, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960737, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960753, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960754, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960773, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960788, "dur": 15, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960805, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960825, "dur": 16, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960844, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960867, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960889, "dur": 13, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960904, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960925, "dur": 16, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960943, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960969, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020960991, "dur": 13, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961006, "dur": 16, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961025, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961042, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961057, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961075, "dur": 15, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961092, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961107, "dur": 13, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961122, "dur": 13, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961137, "dur": 22, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961161, "dur": 46, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961210, "dur": 17, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961229, "dur": 13, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961244, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961263, "dur": 15, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961281, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961296, "dur": 25, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961323, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961344, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961360, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961376, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961398, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961400, "dur": 14, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961416, "dur": 29, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961448, "dur": 15, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961464, "dur": 13, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961479, "dur": 19, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961500, "dur": 14, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961516, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961534, "dur": 73, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961609, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961610, "dur": 25, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961637, "dur": 67, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961706, "dur": 26, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961734, "dur": 1, "ph": "X", "name": "ProcessMessages 1202", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961735, "dur": 22, "ph": "X", "name": "ReadAsync 1202", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961759, "dur": 22, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961783, "dur": 14, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961799, "dur": 14, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961815, "dur": 14, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961831, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961851, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961925, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961928, "dur": 34, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961963, "dur": 1, "ph": "X", "name": "ProcessMessages 1407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020961965, "dur": 121, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962088, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962105, "dur": 19, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962126, "dur": 15, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962142, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962158, "dur": 14, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962174, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962190, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962206, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962224, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962240, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962256, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962272, "dur": 42, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962316, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962333, "dur": 46, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962381, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962397, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962413, "dur": 16, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962430, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962432, "dur": 15, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962449, "dur": 53, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962505, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962530, "dur": 1, "ph": "X", "name": "ProcessMessages 1162", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962531, "dur": 14, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962547, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962564, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962581, "dur": 12, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962595, "dur": 18, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962615, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962640, "dur": 23, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962665, "dur": 13, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962680, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962696, "dur": 15, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962713, "dur": 13, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962728, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962751, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962770, "dur": 18, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962791, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962808, "dur": 15, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962824, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962826, "dur": 14, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962842, "dur": 14, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962858, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962890, "dur": 84, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962976, "dur": 1, "ph": "X", "name": "ProcessMessages 1347", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962977, "dur": 18, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020962997, "dur": 16, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963014, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963030, "dur": 40, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963073, "dur": 13, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963088, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963105, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963121, "dur": 14, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963137, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963153, "dur": 13, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963168, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963182, "dur": 19, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963203, "dur": 14, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963220, "dur": 18, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963240, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963262, "dur": 13, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963278, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963294, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963316, "dur": 14, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963332, "dur": 65, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963398, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963400, "dur": 14, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963415, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963416, "dur": 14, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963432, "dur": 13, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963447, "dur": 14, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963462, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963480, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963499, "dur": 12, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963513, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963528, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963543, "dur": 14, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963559, "dur": 14, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963575, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963599, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963633, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963655, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963672, "dur": 16, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963690, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963707, "dur": 15, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963724, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963749, "dur": 12, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963763, "dur": 19, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963784, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963801, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963821, "dur": 15, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963838, "dur": 16, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963856, "dur": 13, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963871, "dur": 19, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963892, "dur": 14, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963909, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963927, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963945, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963960, "dur": 13, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963975, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020963989, "dur": 14, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964005, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964033, "dur": 44, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964079, "dur": 15, "ph": "X", "name": "ReadAsync 1130", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964096, "dur": 14, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964113, "dur": 14, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964129, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964146, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964164, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964178, "dur": 15, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964195, "dur": 15, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964212, "dur": 14, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964228, "dur": 71, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964300, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964302, "dur": 15, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964318, "dur": 51, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964372, "dur": 14, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964388, "dur": 13, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964403, "dur": 12, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964417, "dur": 15, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964433, "dur": 14, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964449, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964466, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964483, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964499, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964517, "dur": 12, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964532, "dur": 14, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964568, "dur": 19, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964588, "dur": 1, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964589, "dur": 14, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964605, "dur": 14, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964621, "dur": 13, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964635, "dur": 15, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964651, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964666, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964668, "dur": 14, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964684, "dur": 14, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964700, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964718, "dur": 13, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964733, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964754, "dur": 15, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964771, "dur": 18, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964790, "dur": 13, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964806, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964824, "dur": 100, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964926, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964956, "dur": 1, "ph": "X", "name": "ProcessMessages 2341", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964958, "dur": 16, "ph": "X", "name": "ReadAsync 2341", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964976, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020964991, "dur": 24, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965017, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965035, "dur": 15, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965052, "dur": 25, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965079, "dur": 12, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965093, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965108, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965132, "dur": 14, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965148, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965167, "dur": 15, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965184, "dur": 16, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965203, "dur": 12, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965217, "dur": 16, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965235, "dur": 13, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965250, "dur": 17, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965269, "dur": 16, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965286, "dur": 13, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965302, "dur": 13, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965317, "dur": 12, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965331, "dur": 117, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965448, "dur": 1, "ph": "X", "name": "ProcessMessages 2624", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965450, "dur": 16, "ph": "X", "name": "ReadAsync 2624", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965468, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965485, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965502, "dur": 17, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965521, "dur": 13, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965535, "dur": 19, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965556, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965576, "dur": 24, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965602, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965626, "dur": 13, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965641, "dur": 53, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965695, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965728, "dur": 1, "ph": "X", "name": "ProcessMessages 1870", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965730, "dur": 14, "ph": "X", "name": "ReadAsync 1870", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965746, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965761, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965785, "dur": 30, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965816, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965833, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965859, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965882, "dur": 13, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965896, "dur": 16, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965914, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965931, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965947, "dur": 16, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965965, "dur": 14, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020965981, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966003, "dur": 13, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966017, "dur": 15, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966034, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966050, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966066, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966088, "dur": 14, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966104, "dur": 16, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966121, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966139, "dur": 18, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966159, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966183, "dur": 15, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966200, "dur": 15, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966217, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966232, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966253, "dur": 14, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966270, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966289, "dur": 15, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966306, "dur": 14, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966322, "dur": 13, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966337, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966360, "dur": 40, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966405, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966408, "dur": 24, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966434, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966436, "dur": 18, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966457, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966479, "dur": 14, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966494, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966511, "dur": 40, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966553, "dur": 16, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966573, "dur": 15, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966591, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966611, "dur": 14, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966628, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966646, "dur": 12, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966660, "dur": 65, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966729, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966731, "dur": 33, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966766, "dur": 1, "ph": "X", "name": "ProcessMessages 1579", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966768, "dur": 18, "ph": "X", "name": "ReadAsync 1579", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966788, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966790, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966807, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966824, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966844, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966862, "dur": 19, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966883, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966905, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966922, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966939, "dur": 14, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966955, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020966982, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967000, "dur": 15, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967017, "dur": 12, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967031, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967056, "dur": 14, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967072, "dur": 14, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967087, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967105, "dur": 13, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967120, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967135, "dur": 111, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967250, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967253, "dur": 51, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967306, "dur": 3, "ph": "X", "name": "ProcessMessages 2939", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967310, "dur": 19, "ph": "X", "name": "ReadAsync 2939", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967332, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967348, "dur": 29, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967380, "dur": 15, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967397, "dur": 20, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967419, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967445, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967447, "dur": 28, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967477, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967479, "dur": 15, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967497, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967515, "dur": 18, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967534, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967536, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967557, "dur": 15, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967575, "dur": 12, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967589, "dur": 15, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967606, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967622, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967642, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967657, "dur": 39, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967698, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967722, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967742, "dur": 12, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967756, "dur": 79, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967837, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967858, "dur": 13, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967872, "dur": 14, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967889, "dur": 63, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967955, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967975, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020967990, "dur": 13, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968006, "dur": 64, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968071, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968094, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968096, "dur": 22, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968120, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968122, "dur": 14, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968138, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968197, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968223, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968239, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968254, "dur": 60, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968316, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968346, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968365, "dur": 12, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968379, "dur": 55, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968436, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968458, "dur": 15, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968476, "dur": 14, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968492, "dur": 54, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968548, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968591, "dur": 15, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968608, "dur": 12, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968622, "dur": 57, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968681, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968697, "dur": 12, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968711, "dur": 16, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968729, "dur": 13, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968744, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968801, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968819, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968840, "dur": 16, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968858, "dur": 13, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968873, "dur": 51, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968926, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968948, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968949, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020968973, "dur": 57, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969032, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969046, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969069, "dur": 13, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969084, "dur": 61, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969147, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969167, "dur": 13, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969181, "dur": 17, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969200, "dur": 12, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969215, "dur": 56, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969273, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969292, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969309, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969327, "dur": 12, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969342, "dur": 53, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969396, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969413, "dur": 19, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969434, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969449, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969509, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969532, "dur": 17, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969552, "dur": 15, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969569, "dur": 52, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969622, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969641, "dur": 13, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969656, "dur": 37, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969695, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969718, "dur": 13, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969732, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969788, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969805, "dur": 14, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969821, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969838, "dur": 52, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969895, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969899, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969921, "dur": 16, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969939, "dur": 14, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969955, "dur": 29, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020969986, "dur": 14, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970001, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970057, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970074, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970093, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970110, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970111, "dur": 12, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970125, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970177, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970196, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970218, "dur": 13, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970234, "dur": 59, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970295, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970313, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970329, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970345, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970358, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970412, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970436, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970456, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970470, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970530, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970547, "dur": 41, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970590, "dur": 108, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970702, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970742, "dur": 1, "ph": "X", "name": "ProcessMessages 1499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970744, "dur": 28, "ph": "X", "name": "ReadAsync 1499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970778, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970822, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970824, "dur": 16, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970843, "dur": 43, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970890, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970893, "dur": 28, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970922, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020970924, "dur": 536, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971465, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971468, "dur": 111, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971584, "dur": 6, "ph": "X", "name": "ProcessMessages 6955", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971591, "dur": 251, "ph": "X", "name": "ReadAsync 6955", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971846, "dur": 2, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971849, "dur": 93, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971945, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020971948, "dur": 64, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972016, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972019, "dur": 39, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972062, "dur": 2, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972065, "dur": 55, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972128, "dur": 193, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972326, "dur": 2, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972330, "dur": 48, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972382, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972385, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972453, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972456, "dur": 90, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972550, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972553, "dur": 37, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972592, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972594, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972630, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972633, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972656, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972717, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972797, "dur": 2, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972802, "dur": 45, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972850, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972853, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972885, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020972887, "dur": 154, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973046, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973048, "dur": 125, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973176, "dur": 2, "ph": "X", "name": "ProcessMessages 2119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973179, "dur": 41, "ph": "X", "name": "ReadAsync 2119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973223, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973226, "dur": 41, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973274, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973314, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973316, "dur": 11, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973330, "dur": 133, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973468, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973498, "dur": 1, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973500, "dur": 63, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973570, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973606, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973608, "dur": 17, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973628, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973630, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973662, "dur": 100, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973768, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973794, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973796, "dur": 17, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973816, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973887, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973914, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973916, "dur": 19, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020973937, "dur": 101, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974043, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974083, "dur": 1, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974086, "dur": 27, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974116, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974150, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974153, "dur": 65, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974221, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974223, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974245, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974269, "dur": 26, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974297, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974299, "dur": 25, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974326, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974328, "dur": 62, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974395, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974427, "dur": 1, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974429, "dur": 88, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974520, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974522, "dur": 25, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974549, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974550, "dur": 99, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974654, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974677, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974678, "dur": 95, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974778, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974801, "dur": 1, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974802, "dur": 93, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974900, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974937, "dur": 1, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020974940, "dur": 81, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975027, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975050, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975051, "dur": 17, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975072, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975154, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975156, "dur": 88, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975250, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975272, "dur": 1, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975274, "dur": 84, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975363, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975386, "dur": 1, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975388, "dur": 21, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975410, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975431, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975451, "dur": 13, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975465, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975526, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975542, "dur": 13, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975557, "dur": 14, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975594, "dur": 13, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975609, "dur": 35, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975646, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975660, "dur": 18, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975680, "dur": 26, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975709, "dur": 70, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975783, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975803, "dur": 20, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975826, "dur": 64, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975894, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975932, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975934, "dur": 42, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975979, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020975981, "dur": 81, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976066, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976116, "dur": 4, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976123, "dur": 35, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976160, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976162, "dur": 38, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976205, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976238, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976240, "dur": 43, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976287, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976289, "dur": 55, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976348, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976381, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976384, "dur": 18, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976404, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976405, "dur": 62, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976471, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976509, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976510, "dur": 23, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976536, "dur": 58, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976600, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976630, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976633, "dur": 27, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976669, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976676, "dur": 40, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976720, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976722, "dur": 23, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976750, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976752, "dur": 43, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976805, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976810, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976830, "dur": 25, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976858, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976860, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976892, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976893, "dur": 20, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976917, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976919, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976959, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976988, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020976990, "dur": 18, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977010, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977012, "dur": 66, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977084, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977115, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977118, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977142, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977144, "dur": 26, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977173, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977176, "dur": 31, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977210, "dur": 3, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977214, "dur": 25, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977242, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977244, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977263, "dur": 53, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977321, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977361, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977363, "dur": 58, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977431, "dur": 4, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977437, "dur": 44, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977489, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977494, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977521, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977523, "dur": 14, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977541, "dur": 14, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977559, "dur": 82, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977647, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977674, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977676, "dur": 24, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977703, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977706, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977733, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977735, "dur": 24, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977763, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977767, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977790, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977792, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977821, "dur": 23, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977847, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977849, "dur": 52, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977906, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977934, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977936, "dur": 27, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977967, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977969, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977990, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020977992, "dur": 30, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978032, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978038, "dur": 50, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978091, "dur": 2, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978095, "dur": 17, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978115, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978171, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978197, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978199, "dur": 121, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978327, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978344, "dur": 340, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978688, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978723, "dur": 3, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978727, "dur": 24, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978757, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978772, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978787, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978814, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978817, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978837, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978864, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978890, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978892, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978911, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978914, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978933, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978950, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978973, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978975, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020978995, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979013, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979034, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979036, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979067, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979069, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979085, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979110, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979112, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979131, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979134, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979150, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979180, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979208, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979233, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979235, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979254, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979256, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979290, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979308, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979331, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979347, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979403, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979421, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979434, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979450, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979471, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979473, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979493, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979512, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979542, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979558, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979560, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979584, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979585, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979600, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979625, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979642, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979644, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979663, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979666, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979689, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979691, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979711, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979726, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979728, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979749, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979752, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979766, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979781, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979805, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979807, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979833, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979855, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979870, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979891, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979894, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979920, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979924, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979942, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979963, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979965, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979984, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020979997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980000, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980012, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980014, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980038, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980041, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980060, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980082, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980084, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980103, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980125, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980128, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980154, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980178, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980181, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980207, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980209, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980236, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980238, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980253, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980268, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980292, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980307, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980310, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980332, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980335, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980357, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980381, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980398, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980422, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980425, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980458, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980459, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980477, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980479, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980509, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980531, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980534, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980557, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980560, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980586, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980588, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980606, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980608, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980633, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980653, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980671, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980697, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980715, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980717, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980741, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980743, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980764, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980779, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980809, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980812, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980829, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980845, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980862, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980876, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980892, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980915, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980917, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980935, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980936, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980959, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980961, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980987, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020980990, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981019, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981023, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981040, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981042, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981063, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981066, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981092, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981094, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981109, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981111, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981126, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981128, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981144, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981148, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981163, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981179, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981199, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981202, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981219, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981223, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981245, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981246, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981263, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981265, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981286, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981288, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981303, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981317, "dur": 13, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981332, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981334, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981367, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981383, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981388, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981410, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981412, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981434, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981436, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981451, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981467, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981492, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981509, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981511, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981527, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981550, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981552, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981569, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981584, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981600, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981616, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981643, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981645, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981663, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981665, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981681, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981697, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981713, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981715, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981784, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981786, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981808, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981812, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981831, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981834, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981854, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981882, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981885, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981905, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981907, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981923, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981925, "dur": 15, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981944, "dur": 10, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981958, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981987, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020981990, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982011, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982015, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982030, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982032, "dur": 11, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982047, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982097, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982100, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982132, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982135, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982170, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982173, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982193, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982196, "dur": 11, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982209, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982210, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982231, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982233, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982250, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982252, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982268, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982295, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982298, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982321, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982325, "dur": 11, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982340, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982367, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982370, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982413, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982415, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982450, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982454, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982492, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982495, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982527, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982553, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982645, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982667, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982670, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982695, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982697, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982725, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982728, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982749, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982774, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982791, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982807, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982836, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982856, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982869, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982870, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982895, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982915, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982918, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982954, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982972, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982974, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020982989, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983024, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983040, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983042, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983056, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983058, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983071, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983075, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983100, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983102, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983117, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983119, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983144, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983167, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983169, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983184, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983186, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983210, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983212, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983238, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983241, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983259, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983328, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983349, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983368, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983399, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983415, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020983418, "dur": 711, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020984135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020984138, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020984159, "dur": 1034, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020985198, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020985202, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020985221, "dur": 1326, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020986555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020986559, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020986585, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163020986589, "dur": 17263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021003861, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021003865, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021003901, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021003903, "dur": 6278, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010191, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010195, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010224, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010243, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010245, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010279, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010282, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010333, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010480, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021010504, "dur": 24915, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035426, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035429, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035467, "dur": 106, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035578, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035580, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035596, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035943, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021035959, "dur": 254, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036223, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036242, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036343, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036364, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036429, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036458, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036491, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036520, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036521, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036625, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036664, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036667, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036700, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036731, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036760, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036778, "dur": 160, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036942, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021036960, "dur": 345, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037310, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037313, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037354, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037381, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037383, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037398, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037400, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037443, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037466, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037467, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037509, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037523, "dur": 190, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037721, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037761, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037763, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037791, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037793, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037859, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037879, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037931, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037966, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021037986, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038039, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038042, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038067, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038111, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038115, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038156, "dur": 19, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038178, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038243, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038266, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038268, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038296, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038298, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038326, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038327, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038431, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038433, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038481, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038484, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038517, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038547, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038549, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038582, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038586, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038620, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038622, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038658, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038697, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038716, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038816, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038818, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038837, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038853, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038934, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038955, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021038957, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039010, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039042, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039043, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039071, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039128, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039162, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039164, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039184, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039289, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039305, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039307, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039389, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039417, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039420, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039447, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039591, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039624, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039627, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039707, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039746, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039748, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039941, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039943, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021039964, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040261, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040282, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040284, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040529, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040549, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040553, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040598, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040601, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040635, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040674, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040676, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040712, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040714, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040785, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040788, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040824, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040826, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040847, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040849, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040911, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040932, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021040974, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041003, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041005, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041036, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041083, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041123, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041126, "dur": 101, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041230, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041233, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041262, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041265, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041301, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041304, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041429, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041488, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041526, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041560, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041651, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041686, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041890, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041928, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041930, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041974, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021041977, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042007, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042045, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042048, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042090, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042092, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042141, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042143, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042181, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042183, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042269, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042304, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042344, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042377, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042378, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042414, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042417, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042448, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042450, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042482, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042483, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042647, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042686, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042854, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042889, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042891, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042931, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021042933, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043018, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043048, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043089, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043120, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043149, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043289, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043326, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043328, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043379, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043414, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043449, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043452, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043502, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043504, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043538, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043540, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043591, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043630, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043633, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043666, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043667, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043805, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043807, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043848, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043850, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043892, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043894, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043944, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043946, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043979, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021043981, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044017, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044048, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044050, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044127, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044129, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044178, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044179, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044213, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044249, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044251, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044306, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044341, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044373, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044375, "dur": 241, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044621, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044652, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044654, "dur": 243, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044903, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044931, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044933, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021044980, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045034, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045036, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045065, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045066, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045106, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045136, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045138, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045172, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045174, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021045200, "dur": 824, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046032, "dur": 47, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046085, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046090, "dur": 45, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046139, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046293, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046336, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046339, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046540, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046575, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046577, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046619, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046673, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046722, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046725, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046759, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046901, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046905, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046938, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021046973, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047052, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047080, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047111, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047113, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047200, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047230, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047301, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047335, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047336, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047601, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047635, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047637, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047676, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047678, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047708, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047752, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047785, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047900, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047905, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021047928, "dur": 427, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048363, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048367, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048408, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048410, "dur": 169, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048589, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048624, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048626, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048660, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048662, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048791, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048826, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048829, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048876, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048910, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021048912, "dur": 177, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021049092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021049094, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021049114, "dur": 477, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021049596, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021049627, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021049630, "dur": 53794, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021103433, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021103438, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021103462, "dur": 1438, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021104905, "dur": 6444, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021111359, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021111364, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021111414, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021111416, "dur": 705, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112128, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112177, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112203, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112385, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112402, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112522, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112544, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112546, "dur": 171, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112724, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112748, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021112965, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113025, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113028, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113283, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113285, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113307, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113311, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113664, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113666, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113799, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021113802, "dur": 593, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114403, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114429, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114481, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114501, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114714, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114751, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021114753, "dur": 550, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115311, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115333, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115335, "dur": 346, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115689, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115691, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115710, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021115712, "dur": 346, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116063, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116066, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116084, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116086, "dur": 268, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116362, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116422, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116460, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116462, "dur": 407, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116876, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116895, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116969, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021116990, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021117061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021117063, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021117084, "dur": 850, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021117939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021117941, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021117962, "dur": 651, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118619, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118640, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118642, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118677, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118729, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118840, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021118861, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119044, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119072, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119074, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119138, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119162, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119475, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119490, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119726, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021119750, "dur": 429, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120184, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120189, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120224, "dur": 355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120590, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120613, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120953, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120973, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021120975, "dur": 716, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021121696, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021121698, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021121731, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021121733, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021121977, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021121979, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122002, "dur": 428, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122436, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122460, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122815, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021122841, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123099, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123101, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123133, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123135, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123278, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123304, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123306, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123333, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123365, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123367, "dur": 361, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123733, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021123750, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124049, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124079, "dur": 581, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124667, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124712, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124714, "dur": 239, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124958, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124960, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124984, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021124985, "dur": 909, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021125900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021125903, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021125937, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021125939, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021125959, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126002, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126020, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126046, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126088, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126108, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126109, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126206, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126238, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126240, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126270, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126272, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126294, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126309, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126333, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126335, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126355, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126392, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126394, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126415, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126418, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126440, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126442, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126459, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126499, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126529, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126532, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126557, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126588, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126590, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126612, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126614, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126650, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126652, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126719, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126722, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126744, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126746, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126780, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126783, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126804, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126837, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126870, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126872, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126909, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126912, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126932, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126934, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126954, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126980, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021126982, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127003, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127035, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127058, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127060, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127081, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127095, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127096, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127123, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127141, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127142, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127161, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127177, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127191, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127216, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127232, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127234, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127263, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127292, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127294, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127313, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127321, "dur": 14, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127337, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127363, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127366, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127386, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127387, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127425, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127427, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127449, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127451, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127481, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127484, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127532, "dur": 6, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127539, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127649, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127653, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127865, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127912, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127914, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127986, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021127991, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128092, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128099, "dur": 169, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128278, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128284, "dur": 154, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128444, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021128469, "dur": 238552, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021367030, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021367034, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021367087, "dur": 25, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021367113, "dur": 56945, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021424088, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021424096, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021424294, "dur": 9, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021424306, "dur": 187545, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021611861, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021611878, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021611956, "dur": 16, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021611973, "dur": 136991, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021748973, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021748976, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021749063, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021749068, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021749110, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021749114, "dur": 115423, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021864547, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021864551, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021864616, "dur": 32, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021864650, "dur": 4041, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021868700, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021868704, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021868737, "dur": 28, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021868768, "dur": 11483, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021880257, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021880260, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021880278, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021880280, "dur": 1650, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021881935, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021881937, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021881965, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021881980, "dur": 1677, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021883660, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021883681, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021883684, "dur": 745, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021884438, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021884442, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021884469, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021884497, "dur": 70705, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021955214, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021955220, "dur": 195, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021955424, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021955431, "dur": 3829, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021959266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021959269, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021959319, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163021959346, "dur": 407770, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022367138, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022367144, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022367183, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022367187, "dur": 970, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022368163, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022368172, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022368199, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022368227, "dur": 1230, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022369466, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022369470, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022369513, "dur": 603, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754163022370122, "dur": 8849, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 36960, "tid": 69093, "ts": 1754163022396131, "dur": 2047, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 36960, "tid": 8589934592, "ts": 1754163020950799, "dur": 105650, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754163021056451, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754163021056456, "dur": 1149, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 36960, "tid": 69093, "ts": 1754163022398181, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 36960, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 36960, "tid": 4294967296, "ts": 1754163020920152, "dur": 1460853, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754163020928329, "dur": 8608, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754163022381175, "dur": 4501, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754163022383580, "dur": 115, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754163022385741, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 36960, "tid": 69093, "ts": 1754163022398187, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754163020951279, "dur": 1693, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163020952981, "dur": 847, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163020953955, "dur": 92, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754163020954048, "dur": 404, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163020954642, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_E8FF7AECBB376B8C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754163020954933, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_0B408E8BC3EE7994.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754163020955080, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4D750E9CBAFA47AF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754163020955910, "dur": 2140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_685EB25367EADBD0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754163020958583, "dur": 156, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754163020959242, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754163020971049, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754163020971268, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754163020971715, "dur": 146, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754163020954474, "dur": 23267, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163020977758, "dur": 1390204, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163022367994, "dur": 81, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163022368075, "dur": 128, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163022368802, "dur": 129, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163022369015, "dur": 2430, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754163020954537, "dur": 23230, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020977795, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020977994, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_830DC10F39F29699.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163020978504, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020978605, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_D1C7B996A6C45BF4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163020978823, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020979009, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_C2658D8F62234B56.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163020979262, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020979427, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163020979628, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020979743, "dur": 775, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163020980600, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754163020980832, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020980933, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754163020981124, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020981212, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020981345, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754163020981425, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020981796, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020981864, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754163020982005, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020982315, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754163020982479, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020982749, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020983437, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020984086, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020984799, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020985468, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020986573, "dur": 2496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020989070, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020990916, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Advanced\\ModuloNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754163020990851, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020992751, "dur": 3594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020996345, "dur": 3294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163020999640, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021001640, "dur": 3069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021004710, "dur": 2562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021007272, "dur": 3397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021012206, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Runtime\\Debugging\\FrameTiming\\FrameTimeSample.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754163021010670, "dur": 3283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021013953, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021015588, "dur": 4132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021019720, "dur": 2483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021022204, "dur": 2954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021025159, "dur": 3591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021028940, "dur": 979, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Developer\\UpdateReport\\UpdateReportListView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754163021032631, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Configuration\\CredentialsDialog.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754163021028751, "dur": 4737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021033488, "dur": 1467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021034956, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021035563, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021035794, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021035891, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021038035, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021038110, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021038343, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021038571, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021038810, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021038962, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021041432, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021041633, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021041727, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021041819, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021042732, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021042863, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021042996, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021043313, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021043424, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021043572, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021043985, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021044208, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021044316, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021044447, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021044839, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021044969, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754163021045084, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021045361, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021045480, "dur": 2519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021048000, "dur": 60114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021108115, "dur": 2669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021110785, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021110874, "dur": 3053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021113928, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021114041, "dur": 3340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021117383, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021117491, "dur": 4139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021121631, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021121987, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754163021125229, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021125727, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021125960, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021126088, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021126334, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021126551, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021126626, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021126694, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021127035, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754163021127621, "dur": 1240180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020954846, "dur": 22963, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020977819, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_674D8A39E28088FE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163020978292, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020978359, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_12D45AB22F868CD2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163020978486, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020978587, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7CCD9826AD528688.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163020978675, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020978788, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5F1525A9150C0C6C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163020979990, "dur": 1492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020981498, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020981599, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754163020981924, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020982264, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754163020982488, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020982852, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020983540, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020984288, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020984778, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020985421, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020986065, "dur": 2859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020990747, "dur": 904, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Views\\MaterialNodeView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754163020988925, "dur": 3189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020992115, "dur": 859, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Artistic\\Normal\\NormalUnpackNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754163020992115, "dur": 4729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163020996844, "dur": 4542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021001426, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\MemberUnitDescriptor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754163021001386, "dur": 2289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021003676, "dur": 3604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021007281, "dur": 3800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021011082, "dur": 3180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021014263, "dur": 3195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021017459, "dur": 3496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021020956, "dur": 2901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021023857, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021024336, "dur": 3530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021027866, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021030604, "dur": 2252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021032857, "dur": 2094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021034951, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021035714, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163021035922, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021036184, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021037650, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021037991, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163021038420, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021038517, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021039814, "dur": 851, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021040717, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021041639, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163021041858, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021041955, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021043233, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021043426, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021043503, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163021043719, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021043924, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021044907, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021045107, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163021045274, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021045490, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021046161, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021046503, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754163021046656, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021046854, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021047110, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021047230, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021048023, "dur": 60120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021108145, "dur": 3775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021111921, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021112281, "dur": 2461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021114743, "dur": 1206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021115961, "dur": 2705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021118668, "dur": 1829, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021120510, "dur": 3529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021124046, "dur": 1423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754163021125474, "dur": 2413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754163021127942, "dur": 1239847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020955337, "dur": 23575, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020978916, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754163020980195, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754163020980330, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020980527, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020980863, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020980976, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020981068, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754163020981120, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020981249, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020981308, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754163020981782, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020982016, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8883882501886164173.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754163020982111, "dur": 773, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020982890, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020983612, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020984360, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020985198, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020985866, "dur": 2470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020988336, "dur": 2164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020990949, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Round\\TruncateNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754163020990500, "dur": 3602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020994512, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\Shadows\\ShadowProvider\\IEdgeStore.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754163020995052, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\Shadows\\ShadowCasterGroup2DManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754163020994102, "dur": 4600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163020998703, "dur": 2454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021001158, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021003196, "dur": 3563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021006761, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Reflection\\TypeInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754163021006761, "dur": 3595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021010357, "dur": 2818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021013176, "dur": 3331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021016508, "dur": 3331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021019839, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021021411, "dur": 2417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021023829, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021024524, "dur": 3234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021027759, "dur": 2517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021030277, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021032155, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021033125, "dur": 1820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021034945, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021035582, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754163021035889, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021035990, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021037160, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021037661, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754163021038287, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021038513, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021039877, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021040265, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021040396, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754163021040603, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021040657, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021041146, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021041570, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021041737, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754163021041927, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021042400, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021042691, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021043129, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1754163021044075, "dur": 180, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021044778, "dur": 58199, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1754163021108112, "dur": 3565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021111678, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021111762, "dur": 2809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021114572, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021114860, "dur": 3221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021118081, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021118172, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021121839, "dur": 768, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021122620, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754163021125235, "dur": 879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021126123, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021126344, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021126783, "dur": 736, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754163021127521, "dur": 1240236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020954594, "dur": 23187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020977795, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020978299, "dur": 602, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_0E6B7AA98D9EB3C0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163020978902, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_22A35CACB552B292.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163020980345, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020981001, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020981279, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020981331, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754163020981947, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020982044, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020982427, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754163020982794, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020983579, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020984306, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020985042, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020985706, "dur": 1971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020989284, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\Targets\\BuiltInUnlitSubTarget.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754163020990933, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\BuiltInStructFields.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754163020987678, "dur": 4211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020992787, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Input\\Geometry\\ViewVectorNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754163020991889, "dur": 4392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163020996281, "dur": 3856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021000137, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@ffde895a1c6a\\Editor\\Management\\AdaptivePerformanceConfigurationProvider.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754163021000137, "dur": 2864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021004197, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Windows\\GeneratePropertyProvidersWindow\\GeneratePropertyProvidersPage.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754163021003002, "dur": 3835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021006838, "dur": 3789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021010628, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021013326, "dur": 3460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021016787, "dur": 3731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021020519, "dur": 2382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021022903, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021023679, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021024035, "dur": 2674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021026709, "dur": 2956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021029666, "dur": 2528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021032218, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021032627, "dur": 2327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021034954, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021035841, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021036332, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021036733, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021036917, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021037297, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021037418, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021037678, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021038299, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021038667, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021039824, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021040391, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021040611, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021040831, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021041427, "dur": 1025, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021042458, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021043115, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021043403, "dur": 1880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021045285, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021045456, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021045680, "dur": 1581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021047329, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021047440, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021047843, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021047974, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754163021048115, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021048392, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021048459, "dur": 59674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021108135, "dur": 3668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021111804, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021112063, "dur": 3446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021115515, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021115619, "dur": 4500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021120121, "dur": 1090, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021121223, "dur": 4122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021125346, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754163021125452, "dur": 2431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754163021127955, "dur": 1239801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020954655, "dur": 23133, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020977796, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B08776C80E53BDE9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163020978114, "dur": 1909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_5B6051ACD428DB50.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163020980237, "dur": 691, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020981230, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020981296, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754163020981480, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754163020981790, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020982024, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020982235, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754163020983894, "dur": 782, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754163020983644, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020985257, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020985992, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020987564, "dur": 2300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020990751, "dur": 841, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Util\\SlotValueTypeUtil.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754163020992097, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Util\\ScreenSpaceType.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754163020989866, "dur": 3106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020992973, "dur": 4051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020997024, "dur": 2838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163020999862, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021001485, "dur": 2154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021004936, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Threading\\ThreadableAssetWrapper.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754163021003639, "dur": 3729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021007369, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Analysis\\Analyser.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754163021007369, "dur": 3618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021010988, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021012791, "dur": 4070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021016862, "dur": 2453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021019315, "dur": 3826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021023143, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021023851, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021024427, "dur": 2502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021026929, "dur": 3999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021030929, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021033169, "dur": 1790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021034959, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021035585, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163021036161, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021036505, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021037381, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021038206, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021038724, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163021039007, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021039144, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021040390, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021040782, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021041786, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021043111, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021043404, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163021043706, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021043802, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021044463, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021044812, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163021044955, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021045170, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021045990, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021046131, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163021046246, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021046532, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021046651, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754163021046758, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021047082, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021047155, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021047982, "dur": 60147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021108132, "dur": 4635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021112769, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021112838, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021115131, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021115245, "dur": 4711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021119957, "dur": 1566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021121533, "dur": 3991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021125531, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021125716, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021125956, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021126025, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021126096, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021126176, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021126350, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021126741, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021127079, "dur": 247593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021374797, "dur": 370572, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754163021374677, "dur": 372595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021748415, "dur": 156, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754163021749117, "dur": 119095, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754163021883022, "dur": 483554, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754163021883009, "dur": 483570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754163022366610, "dur": 1077, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754163020954761, "dur": 23037, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020977808, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6926138A25866A37.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163020977917, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020977978, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CBBE34600A0DED71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163020978115, "dur": 990, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020979110, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_0B408E8BC3EE7994.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163020979192, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020979250, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_A4F99F59C0C92426.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163020979510, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020979806, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020979883, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020980069, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020980150, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754163020980204, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020980276, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020980399, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163020980779, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163020981018, "dur": 22099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021003119, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021004371, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Utilities\\Paths.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754163021003442, "dur": 4251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021007693, "dur": 3691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021011385, "dur": 3117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021014503, "dur": 2284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021016788, "dur": 4512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021022621, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Attributes\\TrackColorAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754163021021301, "dur": 3586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021024888, "dur": 3659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021028548, "dur": 2462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021031066, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021032865, "dur": 2085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021034950, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021035587, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163021036024, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021036197, "dur": 1174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021037372, "dur": 1570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021038955, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021039175, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021040364, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021040751, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021041796, "dur": 1331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021043127, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021043406, "dur": 4566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021047975, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754163021048197, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021048431, "dur": 59708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021108143, "dur": 4284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021112428, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021112523, "dur": 3289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021115813, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021115918, "dur": 3318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021119290, "dur": 3225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021122517, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021122628, "dur": 3664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754163021126293, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021126854, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021126920, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021127065, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754163021127978, "dur": 1239795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020954897, "dur": 23110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020978289, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F5EC7B27FBB17AFF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754163020978580, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_785B75CD6E98288C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754163020980550, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020980718, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754163020980790, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020980917, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754163020981439, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020981806, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020981991, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020982538, "dur": 3540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020986089, "dur": 2209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020988298, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020990755, "dur": 892, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\NormalMapSpace.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754163020990190, "dur": 2161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020994243, "dur": 842, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Implementation\\SlotType.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754163020992352, "dur": 4494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163020996847, "dur": 3093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021000094, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@ffde895a1c6a\\Editor\\Management\\TypeLoaderExtensions.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754163020999941, "dur": 3718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021004491, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\ResourceProviders\\AssetBundleResourceProvider.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754163021003659, "dur": 3966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021007626, "dur": 3311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021010938, "dur": 4191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021015129, "dur": 3326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021018465, "dur": 3308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021021774, "dur": 2737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021024512, "dur": 2996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021027509, "dur": 3855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021031365, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021033056, "dur": 1892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021034948, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021035572, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754163021035943, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021036270, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021036633, "dur": 1451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021038137, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021039053, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021039497, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021040456, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021040795, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021041775, "dur": 1333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021043145, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021043431, "dur": 4564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021047995, "dur": 60124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021108121, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021110529, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021110900, "dur": 3248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021114150, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021114287, "dur": 3615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021117903, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021118401, "dur": 4405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021122808, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021123297, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754163021126484, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021126792, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021127027, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754163021127552, "dur": 1240181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020954958, "dur": 23167, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020978382, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020978758, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_DDA8E091E3967B34.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163020978886, "dur": 1822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_61C77FC2752CA928.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163020980780, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020981365, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020981429, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020981721, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020981798, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020981865, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754163020982058, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020982750, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020983897, "dur": 829, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\mscorrc.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754163020983565, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020984727, "dur": 949, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@7d01a2258732\\Rider\\Editor\\LoggingLevel.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754163020984727, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020986024, "dur": 2921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020988945, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020990932, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Wave\\NoiseSineWaveNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754163020990452, "dur": 2756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020993208, "dur": 4416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020997624, "dur": 2076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163020999700, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021000900, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021002857, "dur": 2701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021006602, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\TextureResolution.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754163021005559, "dur": 3366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021008925, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021011233, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021013443, "dur": 3372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021016816, "dur": 3232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021020049, "dur": 3294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021023344, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021024278, "dur": 3756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021028034, "dur": 2880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021030915, "dur": 2198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021033114, "dur": 1830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021034944, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021035562, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163021035862, "dur": 1093, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021036959, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021038097, "dur": 2262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021040411, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163021040588, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021040645, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021041325, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021041504, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021041716, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163021041914, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021042017, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021043027, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021043193, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021043387, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163021043612, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021043680, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021044049, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021044531, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163021044671, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021045120, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021045279, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754163021045411, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021045715, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021045849, "dur": 2153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021048003, "dur": 60108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021108113, "dur": 3448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021111562, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021111677, "dur": 6521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021118199, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021118604, "dur": 4189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021122794, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021122892, "dur": 2535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754163021125428, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021125650, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021126223, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021126545, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021126932, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021127008, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021127062, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754163021127971, "dur": 1239897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020955006, "dur": 23279, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020978745, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2A749ACEEBE3A48E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754163020978886, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_82714E7E5E4D2D26.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754163020980686, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754163020981453, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020981969, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754163020982046, "dur": 2702, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020984762, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020985137, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020985491, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020986733, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020987574, "dur": 2394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020990615, "dur": 1028, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Util\\KeywordCollector.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163020989969, "dur": 2343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020992312, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020993094, "dur": 4666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163020997760, "dur": 2775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021000536, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021004355, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@ffde895a1c6a\\Runtime\\Core\\AdaptivePerformanceInit.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163021005477, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Windows\\TabbedPage.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163021002965, "dur": 3257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021006997, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Special\\UnknownInspector.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163021006223, "dur": 3567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021009816, "dur": 3875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021013692, "dur": 3600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021017292, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseOverMessageListener.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163021017292, "dur": 3426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021020718, "dur": 2492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021023211, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021023740, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021024268, "dur": 3373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021027642, "dur": 3864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021031507, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@388540dd9ce6\\UnityEditor.TestRunner\\TestRunner\\Messages\\ExitPlayMode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163021033067, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@388540dd9ce6\\UnityEditor.TestRunner\\TestRunner\\Callbacks\\WindowResultUpdaterDataHolder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754163021031506, "dur": 3243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021034749, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021034992, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021035582, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754163021035983, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021036076, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021037031, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021037277, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021037490, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754163021037665, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021037840, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754163021038037, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021038144, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021039501, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021039807, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021040685, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021041032, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021041795, "dur": 1330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021043125, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021043387, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754163021043680, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021043773, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021044399, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021044639, "dur": 3359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021047998, "dur": 60111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021108111, "dur": 3522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021111634, "dur": 1546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021113188, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021115358, "dur": 3671, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021119039, "dur": 3491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021122531, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754163021122849, "dur": 4685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754163021127594, "dur": 1240336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020955134, "dur": 23627, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020978822, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020979988, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020980666, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020981452, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020981925, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754163020981995, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754163020982059, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020982751, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020983136, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020983470, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020983829, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020984174, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020984500, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020984864, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020985181, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020985504, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020985845, "dur": 2996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020990904, "dur": 692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Views\\Slots\\LabelSlotControlView.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754163020988842, "dur": 3588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020994664, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Graphs\\PropertyConnectionStateMaterialSlot.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754163020992431, "dur": 3162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020995594, "dur": 3014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163020998608, "dur": 2065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021000674, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021001818, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021004355, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Runtime\\GPUDriven\\InstanceOcclusionCullerShaderVariables.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754163021002567, "dur": 2362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021005615, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\BoltCorePaths.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754163021004930, "dur": 2282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021007213, "dur": 2560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021009810, "dur": 2940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021012750, "dur": 3092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021015843, "dur": 3685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021019528, "dur": 3191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021022720, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021023869, "dur": 3003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021026873, "dur": 3496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021030369, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021032628, "dur": 2330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021034958, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021035582, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754163021035826, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021036324, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021037234, "dur": 864, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021038118, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021038321, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754163021038492, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754163021038838, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021038944, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021040519, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021040756, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021041735, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754163021041888, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021041972, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021042428, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021042580, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021043051, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021043126, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021043385, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754163021043507, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021043693, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021044347, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021044758, "dur": 3217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021047993, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754163021048129, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021048196, "dur": 59940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021108146, "dur": 3640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021111788, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021112066, "dur": 6018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021118086, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021118190, "dur": 3122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021121313, "dur": 2288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021123612, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754163021126453, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021126591, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1754163021126849, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021127020, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754163021127409, "dur": 1240529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020955279, "dur": 23629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020978912, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A9644AD1997CC33F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754163020980294, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754163020980493, "dur": 447, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754163020981174, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754163020981580, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754163020981778, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020981990, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020982677, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020982798, "dur": 1131, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\Converter\\PPv2\\EffectConverters\\DepthOfFieldConverter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754163020982798, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020984572, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020985337, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020985948, "dur": 3159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020990956, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Inspector\\PropertyDrawers\\EnumPropertyDrawer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754163020989107, "dur": 2850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020993191, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Channel\\SwizzleNode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754163020991957, "dur": 2910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020994868, "dur": 4455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163020999323, "dur": 3318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021004428, "dur": 897, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Runtime\\GPUDriven\\GPUResidentDrawer.Validator.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754163021002642, "dur": 3493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021006136, "dur": 3826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021009963, "dur": 3906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021013870, "dur": 3869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021017739, "dur": 3945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021021685, "dur": 2133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021023819, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021024488, "dur": 2479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021026967, "dur": 3548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021030516, "dur": 2644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021033161, "dur": 1778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021034991, "dur": 1099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021036091, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754163021036523, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021038570, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021038676, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021038746, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754163021038946, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021039002, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021039880, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021040133, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754163021040337, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021040799, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021041401, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021041733, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754163021041902, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021042451, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021042688, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021043118, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021043405, "dur": 4574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021047980, "dur": 11974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021059955, "dur": 48682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021108640, "dur": 2827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021111468, "dur": 832, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021112307, "dur": 4235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021116543, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021116629, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021119281, "dur": 849, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021120146, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021122802, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021122920, "dur": 2917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754163021125838, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021125997, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021126244, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021126540, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021126924, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021127046, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754163021127608, "dur": 1240123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020955037, "dur": 23276, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020978853, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_03F87B6D11395095.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754163020979968, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020980682, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754163020980916, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020981335, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754163020981470, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754163020981608, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020981699, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754163020981776, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020981858, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754163020981945, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754163020982045, "dur": 911, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020982960, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020983310, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020983660, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020984251, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020984601, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020985147, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020985783, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020986949, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020988263, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020990958, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Utility\\Logic\\BranchOnInputConnection.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754163020990004, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020992345, "dur": 2401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020994746, "dur": 3617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163020998363, "dur": 2116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021000480, "dur": 2863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021003344, "dur": 2441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021005785, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021007239, "dur": 3558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021010798, "dur": 2525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021013324, "dur": 2548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021015872, "dur": 3193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021019066, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021021371, "dur": 2831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021024202, "dur": 3188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021027391, "dur": 4269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021031660, "dur": 2633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021034293, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021034954, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021035564, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754163021035762, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021036208, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754163021037325, "dur": 820, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021038152, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021038618, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754163021038883, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021039262, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754163021040310, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021040571, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021040737, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021041754, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021042869, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754163021043036, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021043150, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754163021043696, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021044177, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021044481, "dur": 3496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021047978, "dur": 9156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021058714, "dur": 195, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 12, "ts": 1754163021058910, "dur": 984, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 12, "ts": 1754163021059894, "dur": 50, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 12, "ts": 1754163021057135, "dur": 2813, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021059948, "dur": 50628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021110578, "dur": 2838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754163021113417, "dur": 845, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021114284, "dur": 9501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754163021123785, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021124264, "dur": 3169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754163021127434, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754163021127514, "dur": 1240420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020955066, "dur": 23326, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020978395, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3EC188A9A2685E71.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163020978844, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020978970, "dur": 1051, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_2BE1F67E8EB9FC15.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163020980429, "dur": 787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020981314, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754163020981543, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020981813, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020981885, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754163020981937, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754163020982023, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020982917, "dur": 1185, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Editor\\Editors\\CinemachineStateDrivenCameraEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754163020984102, "dur": 1832, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Editor\\Editors\\CinemachineSmoothPathEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754163020982770, "dur": 3473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020986243, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020988015, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020989570, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020990939, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Nodes\\Math\\Derivative\\DDXYNode.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754163020990764, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020992398, "dur": 3192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020995591, "dur": 2931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163020998523, "dur": 1997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021000520, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021002698, "dur": 2641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021005340, "dur": 2536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021007877, "dur": 3610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021011488, "dur": 2387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021013876, "dur": 3142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021017019, "dur": 2512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021019531, "dur": 3352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021022885, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021024591, "dur": 3612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021028204, "dur": 3117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021031321, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021033378, "dur": 1564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021034943, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021035570, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163021035690, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021035785, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021036194, "dur": 805, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021037069, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163021037329, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163021037538, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163021037663, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021038086, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021038155, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163021038349, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021038852, "dur": 1807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021040660, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021041106, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021041170, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021041740, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754163021042023, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021042191, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021042712, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021042930, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021043135, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021043413, "dur": 4580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021047993, "dur": 60123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021108117, "dur": 3764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021111882, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021111949, "dur": 4512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021116462, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021116527, "dur": 1942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021118470, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021118704, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021121218, "dur": 3000, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021124228, "dur": 3074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754163021127390, "dur": 755647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754163021883081, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754163021883044, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754163021883168, "dur": 789, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754163021883961, "dur": 483907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020955163, "dur": 23708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020978874, "dur": 1590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E44D737DF0BCE080.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754163020980498, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754163020980897, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020981385, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020981945, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754163020982013, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020982204, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7043721260763788226.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754163020982669, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020982931, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020983286, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020983922, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020984299, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020984666, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020985002, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020985314, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020986256, "dur": 3278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020990942, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Controls\\EnumConversionControl.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754163020989535, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020991688, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020993060, "dur": 4060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163020997121, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021000111, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Editor\\Lighting\\Shadow\\ShadowCascadeGUI.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754163020999089, "dur": 3664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021002754, "dur": 2116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021004870, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021007050, "dur": 2318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021009368, "dur": 2669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021012037, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021013924, "dur": 3402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021017327, "dur": 3865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021021193, "dur": 3529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021024722, "dur": 3621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021028344, "dur": 2565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021030910, "dur": 2459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021033370, "dur": 1587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021034958, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021035579, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754163021035952, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021036058, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021037451, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021037798, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021038261, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754163021038613, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021038748, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021039739, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021040125, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754163021040349, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021040796, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021041336, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021041451, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021041761, "dur": 1348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021043142, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021043400, "dur": 1711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021045112, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754163021045276, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021045357, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021045825, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021046278, "dur": 1710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021047988, "dur": 60130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021108119, "dur": 2668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021110788, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021110871, "dur": 3031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021113903, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021114266, "dur": 588, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021114857, "dur": 3246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021118104, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021118241, "dur": 4052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021122294, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021122363, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754163021124858, "dur": 955, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021126024, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021126461, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021126785, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021126960, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754163021127040, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021127115, "dur": 752572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754163021879728, "dur": 74776, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754163021879688, "dur": 74819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754163021954541, "dur": 4273, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754163021958820, "dur": 409021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163020955619, "dur": 23307, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163020978926, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7C8AF205DD25174E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163020980778, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163020980940, "dur": 28307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021009249, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021009725, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021009859, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163021010034, "dur": 24780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021034815, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021034942, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021035021, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163021035134, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021035432, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021035563, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163021035681, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021036277, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021037377, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021037883, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163021038408, "dur": 1621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021040031, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021040404, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163021040563, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021040804, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021041337, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021041918, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021043133, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021043427, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754163021043674, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021043901, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021044639, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021045037, "dur": 2976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021048013, "dur": 60110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021108127, "dur": 4121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021112249, "dur": 1689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021113948, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021116278, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021116429, "dur": 2708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021119138, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021119703, "dur": 2838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021122542, "dur": 1963, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021124514, "dur": 2983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754163021127498, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754163021127616, "dur": 1240295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020955536, "dur": 23382, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020978929, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_AE2A3D5B7498DE25.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754163020980219, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020980420, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020980501, "dur": 714, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754163020981230, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020981299, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754163020981403, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754163020981460, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020981800, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020981899, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020981984, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020982732, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020982851, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020983531, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020984171, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020984898, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020987162, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\2D\\Converter\\BuiltInToURP2DConverterContainer.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754163020988173, "dur": 561, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\2D\\CinemachineUniversalPixelPerfectEditor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754163020990868, "dur": 732, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Util\\CopyPasteGraph.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754163020992434, "dur": 694, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\ShaderGUI\\GenericShaderGraphMaterialGUI.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754163020987162, "dur": 7007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020994609, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\Passes\\Utility\\RendererLighting.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754163020994169, "dur": 4538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163020998708, "dur": 2261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021000969, "dur": 3569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021004539, "dur": 4450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021008990, "dur": 3064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021012055, "dur": 2449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021014505, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021016284, "dur": 2904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021019188, "dur": 2669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021021858, "dur": 3744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021025603, "dur": 3078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021028682, "dur": 3192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021031875, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021034010, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021034957, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021036056, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754163021036996, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021037076, "dur": 1317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021038394, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021038726, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754163021038851, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021038955, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021040116, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021040408, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021040471, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754163021040718, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021040848, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021041394, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021041605, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021041781, "dur": 1331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021043112, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021043391, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754163021043642, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021043747, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021044469, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021044850, "dur": 3120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021047971, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754163021048132, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021048345, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021048564, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021048650, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021049668, "dur": 316756, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021375024, "dur": 47809, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754163021374552, "dur": 48349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021423301, "dur": 132, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021424261, "dur": 187093, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021614435, "dur": 13572, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754163021628010, "dur": 117305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754163021614420, "dur": 132592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021748054, "dur": 159, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754163021748979, "dur": 115104, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754163021879698, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754163021879683, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754163021879814, "dur": 1628, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754163021881447, "dur": 486316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754163022376772, "dur": 1372, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 36960, "tid": 69093, "ts": 1754163022398753, "dur": 10815, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 36960, "tid": 69093, "ts": 1754163022409640, "dur": 1991, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 36960, "tid": 69093, "ts": 1754163022393020, "dur": 19346, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}