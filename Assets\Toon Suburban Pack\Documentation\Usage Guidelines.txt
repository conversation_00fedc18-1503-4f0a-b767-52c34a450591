Thank you for purchasing TOON Suburban pack!
Very glad to have you as a customer :)


	At first glance, the pack may look daunting, with over 800 prefabs to choose from, but hopefully, 
the folder structure and naming convention will make things easy to pick up.
	(BuiltIn ONLY): If you install the post processing stack after importing this package, some errors might appear, depending on the version of Unity 
that you're using. A Unity restart usually solves those issues. (if not, please re-import this pack in a project where post processing 
stack is installed).



		Naming convention:


	The assets in this package use the "TSP" prefix, in case you're using other packs from the TOON Series, to be able to easily 
identify the origin of an asset.
	The last letter in the name, usually is a capital letter, such as :"TSP_Tree_02A". It signifies the color scheme of the asset.
If there are more color schemes for an asset, the naming would probably continue as follows: "TSP_Tree_02B", "TSP_Tree_02C".

	In the case of wheels of vehicles, character rig bones, and other animateable assets, the suffixes ".L" and ".R" are used. 
For example, the rear left wheel of a car would be named: "TSP_Sedan_01B_Wheel_R.L".


		Prefabs:


	The source files are contained in the "Models" folder, along with the custom mesh colliders, in the "Colliders" sub-folder. 
Custom mesh colliders were used where the shape of the asset was too complicated to use simple "box" or "sphere" colliders.

	The Prefabs are structured in multiple categories. Most of the assets are modular, so some prefab categories are dedicated to
pre-assembled structures, such as "House Presets". You can always build your own custom structures, using the modular assets.


		Scene:


	There is a scene included in this pack, with a plethora of exterior and interior assets.

	To achieve the lighting effect there is a post-processing profile included in the project, which requires the post processing stack to be installed.
(if you're using URP, the post processing stack isn't needed).
	

		Characters:


	There are 6 character templates (3 male & 3 female), located in the "Characters Source" folder. The templates have customizable 
items (clothes, accessories, skin color, ...) which you can activate/deactivate as you please, thus being able to create numerous variants.
Some pre-made variants have been saved as prefabs in the "Characters Variants" folder.

	The characters have no animations included in the package. 
The rigging system is "Humanoid", which allows for the use of external animations, either from the Asset Store, or other external sources, 
such as "Mixamo.com".


		Vehicles:


	All the vehicles come with interiors, detached wheels, doors and other working items.


		Interior props:


	This pack comes with a wide array of interior assets to help populate your game world with even more detail.
	Some considerations regarding the use: Most of the furniture comes with extra functions and other logical mechanisms, such as: 
functioning fridge doors, functioning microwave door, working drawers, animateable hands for clocks and many more.



* If you like the pack, please review it :) *
