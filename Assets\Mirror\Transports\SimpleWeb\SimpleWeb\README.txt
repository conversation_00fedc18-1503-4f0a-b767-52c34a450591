SimpleWebTransport is a Transport that implements websocket for Webgl
Can be used in High level networking solution like Mirror or Mirage
This transport can also work on standalone builds and has support for 
encryption with websocket secure.

Requirements:
  Unity 2019.4 LTS

Documentation:
  https://mirror-networking.gitbook.io/docs/
  https://github.com/<PERSON>-<PERSON>en/SimpleWebTransport/blob/master/README.md

Support:
  Discord: https://discord.gg/BZTQcftBkE
  Bug Reports: https://github.com/<PERSON><PERSON>/SimpleWebTransport/issues


**To get most recent updates and fixes download from github**
https://github.com/<PERSON>-<PERSON>/SimpleWebTransport/releases
